from contextlib import asynccontextmanager

from biz.yishuitong.service import yishuitong_report_generate_router
from fastapi import FastAPI
import uvicorn


@asynccontextmanager
async def lifespan(app: FastAPI):
    from playwright.async_api import async_playwright

    playwright = await async_playwright().start()
    browser = await playwright.chromium.launch()
    app.state.playwright = playwright
    app.state.browser = browser
    try:
        yield
    finally:
        await browser.close()
        await playwright.stop()

app = FastAPI(lifespan=lifespan)
app.include_router(yishuitong_report_generate_router)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
 