import asyncio
import json
from contextlib import asynccontextmanager

from biz.yishuitong.model import CreateTaxReportRequest
from biz.yishuitong.report_generator import generate_report, generate_html_report, save_html_to_pdf
from biz.yishuitong.service import fetch_data
from fastapi import Request, FastAPI

req = CreateTaxReportRequest(
    orderId="123",
    taxpayerId="91350583MADDX89793",
    taxNo="TAX_NO_20250514101506396ZRl",
    email="<EMAIL>",
)

# req = CreateTaxReportRequest(
#     orderId="123",
#     taxpayerId="91440300MA5HBRY1XB",
#     taxNo="TAX_NO_20250425111323400iEB",
#     email="<EMAIL>",
# )


@asynccontextmanager
async def lifespan(app: FastAPI):
    from playwright.async_api import async_playwright

    playwright = await async_playwright().start()
    browser = await playwright.chromium.launch()
    app.state.playwright = playwright
    app.state.browser = browser
    try:
        yield
    finally:
        await browser.close()
        await playwright.stop()

app = FastAPI(lifespan=lifespan)

request_scope = {
    "type": "http",
    "asgi": {"version": "3.0", "spec_version": "2.3"}, # ASGI 版本信息
    "http_version": "1.1",
    "server": ("127.0.0.1", 8000), # 模拟服务器地址和端口
    "client": ("127.0.0.1", 50000), # 模拟客户端地址和端口
    "scheme": "http",
    "method": "POST",
    "path": "/your/simulated/path", # 模拟请求路径
    "raw_path": b"/your/simulated/path",
    "query_string": b"", # 模拟查询字符串
    "root_path": "", # 根路径
    "headers": [ # 请求头
        (b"user-agent", b"ManualRequestClient/1.0"),
        (b"content-type", b"application/json"),
        (b"accept", b"application/json"),
    ],
    "state": {},  # 这是 request 自身的 state，与 app.state 不同
    "app": app,  # <--- 关键：将 FastAPI 应用实例传递给 scope
}

if __name__ == "__main__":
    async def main():
        async with lifespan(app):
            #fapiao_data, tax_data = await fetch_data(req)
            # with open("./fapiao_data.json", "w") as f:
            #     f.write(json.dumps(fapiao_data, indent=2, ensure_ascii=False))
            # with open("./tax_data.json", "w") as f:
            #     f.write(json.dumps(tax_data, indent=2, ensure_ascii=False))
            with open("./fapiao_data.json", "r") as f:
                fapiao_data = json.load(f)
            with open("./tax_data.json", "r") as f:
                tax_data = json.load(f)
            temp_dir = "./"
            sections, summary_text, total_risk_num = await generate_report(req, fapiao_data, tax_data, temp_dir)
            results = await generate_html_report(sections, summary_text, total_risk_num)
            # with open("./report.html", "r") as f:
            #     results = f.read()
            request: Request = Request(request_scope)
            pdf_path = "./报告_new.pdf"
            await save_html_to_pdf(pdf_path, results, request)
    asyncio.run(main())
