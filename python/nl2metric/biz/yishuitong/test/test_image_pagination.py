#!/usr/bin/env python3
"""
测试图片分页问题修复效果
"""
import asyncio
import os
from contextlib import asynccontextmanager
from fastapi import Request, FastAPI

# 测试HTML内容，模拟报告概述部分的图片分页问题
TEST_HTML = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试PDF图片分页和Logo</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .page { min-height: 800px; margin-bottom: 50px; }
        h1 { color: #333; }
        p { line-height: 1.6; }
        .image-container { 
            max-height: 200mm; 
            overflow: hidden; 
            page-break-inside: auto;
            text-align: center;
            margin: 20px 0;
        }
        .test-image {
            max-width: 90%; 
            max-height: 160mm; 
            object-fit: contain; 
            display: block;
            margin: 0 auto;
            background-color: #f0f0f0;
            border: 2px solid #ccc;
        }
    </style>
</head>
<body>
    <div class="page">
        <h1>报告概述测试</h1>
        <p>这是报告概述部分的内容。测试图片是否会被强制推到下一页。</p>
        <p>以下应该有一个测试图片，它应该在当前页面显示，而不是被推到下一页：</p>
        
        <div class="image-container">
            <div class="test-image" style="width: 400px; height: 300px; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                <span>测试图片 - 风险雷达图</span>
            </div>
        </div>
        
        <p>图片后的内容应该正常显示，不应该有很大的空白。</p>
        <p>这段文字应该紧跟在图片后面，证明分页控制正常。</p>
    </div>
    
    <div class="page">
        <h1>第二页测试</h1>
        <p>这是第二页的内容。每一页的底部都应该显示相同的logo和横线。</p>
        
        <div class="image-container">
            <div class="test-image" style="width: 500px; height: 200px; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                <span>测试图片 - 风险维度表</span>
            </div>
        </div>
        
        <p>测试图片分页控制是否正常工作。</p>
        <p>图片应该合理地放置在页面中，不会造成过多空白。</p>
    </div>
    
    <div class="page">
        <h1>第三页测试 - 长内容</h1>
        <p>这是第三页的内容。验证多页PDF中logo和横线的一致性。</p>
        <p>同时验证图片不会造成过大的空白。</p>
        
        <!-- 模拟长内容 -->
        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
        <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
        <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
        
        <div class="image-container">
            <div class="test-image" style="width: 600px; height: 400px; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                <span>较大的测试图片</span>
            </div>
        </div>
        
        <p>这段文字应该在图片后正常显示。</p>
    </div>
</body>
</html>
"""

@asynccontextmanager
async def lifespan(app: FastAPI):
    from playwright.async_api import async_playwright
    
    playwright = await async_playwright().start()
    browser = await playwright.chromium.launch()
    app.state.playwright = playwright
    app.state.browser = browser
    try:
        yield
    finally:
        await browser.close()
        await playwright.stop()

async def test_image_pagination():
    """测试图片分页问题修复是否有效"""
    app = FastAPI(lifespan=lifespan)
    
    # 模拟request对象
    request_scope = {
        "type": "http",
        "asgi": {"version": "3.0", "spec_version": "2.3"},
        "http_version": "1.1",
        "server": ("127.0.0.1", 8000),
        "client": ("127.0.0.1", 50000),
        "scheme": "http",
        "method": "POST",
        "path": "/test",
        "raw_path": b"/test",
        "query_string": b"",
        "root_path": "",
        "headers": [
            (b"user-agent", b"TestClient/1.0"),
            (b"content-type", b"application/json"),
        ],
        "state": {},
        "app": app,
    }
    
    async with lifespan(app):
        # 导入修复后的函数
        from biz.yishuitong.report_generator import save_html_to_pdf
        
        request = Request(request_scope)
        output_path = "./test_image_pagination.pdf"
        
        print("开始生成图片分页测试PDF...")
        await save_html_to_pdf(output_path, TEST_HTML, request)
        
        if os.path.exists(output_path):
            print(f"✅ 测试PDF生成成功: {output_path}")
            print("请打开PDF文件检查:")
            print("  1. 每页底部是否显示了logo图片和彩色横线")
            print("  2. 图片是否正常显示在页面中，没有被强制推到下一页")
            print("  3. 图片前后是否没有过大的空白")
            print("  4. 页面内容是否正常分页")
        else:
            print("❌ 测试PDF生成失败")

if __name__ == "__main__":
    asyncio.run(test_image_pagination())
