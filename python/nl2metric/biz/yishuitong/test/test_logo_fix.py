#!/usr/bin/env python3
"""
简单测试脚本，验证PDF底部logo和横线是否正确显示
"""
import asyncio
import os
from contextlib import asynccontextmanager
from fastapi import Request, FastAPI

# 简单的HTML内容用于测试
TEST_HTML = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试PDF Logo</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .page { height: 800px; margin-bottom: 50px; }
        h1 { color: #333; }
        p { line-height: 1.6; }
    </style>
</head>
<body>
    <div class="page">
        <h1>第一页测试内容</h1>
        <p>这是第一页的内容。我们需要验证在PDF的底部是否显示了logo和彩色横线。</p>
        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
    </div>
    
    <div class="page">
        <h1>第二页测试内容</h1>
        <p>这是第二页的内容。每一页的底部都应该显示相同的logo和横线。</p>
        <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
    </div>
    
    <div class="page">
        <h1>第三页测试内容</h1>
        <p>这是第三页的内容。验证多页PDF中logo和横线的一致性。</p>
        <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
    </div>
</body>
</html>
"""

@asynccontextmanager
async def lifespan(app: FastAPI):
    from playwright.async_api import async_playwright
    
    playwright = await async_playwright().start()
    browser = await playwright.chromium.launch()
    app.state.playwright = playwright
    app.state.browser = browser
    try:
        yield
    finally:
        await browser.close()
        await playwright.stop()

async def test_logo_fix():
    """测试logo修复是否有效"""
    app = FastAPI(lifespan=lifespan)
    
    # 模拟request对象
    request_scope = {
        "type": "http",
        "asgi": {"version": "3.0", "spec_version": "2.3"},
        "http_version": "1.1",
        "server": ("127.0.0.1", 8000),
        "client": ("127.0.0.1", 50000),
        "scheme": "http",
        "method": "POST",
        "path": "/test",
        "raw_path": b"/test",
        "query_string": b"",
        "root_path": "",
        "headers": [
            (b"user-agent", b"TestClient/1.0"),
            (b"content-type", b"application/json"),
        ],
        "state": {},
        "app": app,
    }
    
    async with lifespan(app):
        # 导入修复后的函数
        from biz.yishuitong.report_generator import save_html_to_pdf
        
        request = Request(request_scope)
        output_path = "./test_logo_output.pdf"
        
        print("开始生成测试PDF...")
        await save_html_to_pdf(output_path, TEST_HTML, request)
        
        if os.path.exists(output_path):
            print(f"✅ 测试PDF生成成功: {output_path}")
            print("请打开PDF文件检查每页底部是否显示了logo和彩色横线")
        else:
            print("❌ 测试PDF生成失败")

if __name__ == "__main__":
    asyncio.run(test_logo_fix())
